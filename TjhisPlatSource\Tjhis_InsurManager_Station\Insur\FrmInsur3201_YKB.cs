using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Tjhis.InsurManager.Station.Comm;

namespace Tjhis.InsurManager.Station.Insur
{
    public partial class FrmInsur3201_YKB : PlatCommon.SysBase.ParentForm
    {
        public FrmInsur3201_YKB()
        {
            InitializeComponent();
        }
        //DataTable DT = new DataTable();

        private readonly DateTime dtBegin = new DateTime();
        private readonly DateTime dtEnd = new DateTime();

        public FrmInsur3201_YKB(DateTime dtBegin, DateTime dtEnd)
        {
            this.dtBegin = dtBegin;
            this.dtEnd = dtEnd;
            InitializeComponent();
        }
        //public FrmInsur3201_YKB(DataTable _DT)
        //{
        //    InitializeComponent();
        //    this.DT = _DT;
        //}

        private void FrmInsur3201_YKB_Load(object sender, EventArgs e)
        {
            //GC_YKB.DataSource = DT;
            queryYKB();
        }

        /// <summary>
        /// 查询渝快保
        /// </summary>
        private void queryYKB()
        {           
            string startime = this.dtBegin.ToString("yyyy-MM-dd 00:00:00");
            string endtime = this.dtEnd.ToString("yyyy-MM-dd 23:59:59");
            string sql = string.Format(@"select to_char(rownum) 序号,OUT_SETLINFO_MDTRT_ID 就诊流水号,out_setlinfo_SETL_ID 医保结算流水号,out_setlinfo_psn_name 姓名,out_setlinfo_certno 证件号码,
                        inf_day 出院日期, inf_time 结算时间,jb 确诊疾病, OUT_SETLINFO_MEDFEE_SUMAMT 医疗费总金额,out_setlinfo_hifes_pay 渝快保赔付金额
                        from(
                        select OUT_SETLINFO_MDTRT_ID, out_setlinfo_SETL_ID, out_setlinfo_psn_name, out_setlinfo_certno,
                        to_char(to_date(inf_time, 'yyyy-mm-dd hh24:mi:ss'), 'yyyy-mm-dd') inf_day, inf_time
                        , (select to_char(wmsys.wm_concat(d.diagnosis_desc))
                           from diagnosis d
                           where d.patient_id = inp.patient_id
                           and d.visit_id = inp.visit_id
                           and d.diagnosis_type = '2'
                           and rownum <= 3) jb, OUT_SETLINFO_MEDFEE_SUMAMT, out_setlinfo_hifes_pay
                        from INP_SAVE_PTYB inp where inp.out_setlinfo_hifes_pay > 0 and inp.inf_time>='{0}' and inp.inf_time<='{1}'
                        union all
                        select OUT_SETLINFO_MDTRT_ID, out_setlinfo_SETL_ID, out_setlinfo_psn_name, out_setlinfo_certno,
                        to_char(to_date(inf_time, 'yyyy-mm-dd hh24:mi:ss'), 'yyyy-mm-dd') inf_day, inf_time
                        , (select to_char(wmsys.wm_concat(od.diagnosis_desc))
                           from outp_diagnosis od
                           where od.clinic_no = outp.clinic_no
                           and rownum <= 3) jb, OUT_SETLINFO_MEDFEE_SUMAMT, out_setlinfo_hifes_pay
                        from OUTP_SAVE_PTYB outp where outp.out_setlinfo_hifes_pay > 0 and outp.inf_time>='{0}' and outp.inf_time<='{1}'
                        ) a", startime, endtime);
            DataTable dtYkb = new NM_Service.NMService.ServerPublicClient().GetDataBySql(sql).Tables[0];
            GC_YKB.DataSource = dtYkb;
        }
        private void barButtonItem2_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void barButtonItem1_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            //SaveFileDialog saveFileDialog = new SaveFileDialog
            //{
            //    Filter = "Xls Files|*.xls",
            //    Title = "导出到 Xls 文件",
            //    FileName = $"渝快保对账{DateTime.Now.ToString("yyyyMMddHHmmss")}.xls"
            //};
            //if (saveFileDialog.ShowDialog() == DialogResult.OK)
            //{
            //    DevExpress.XtraPrinting.XlsExportOptions options = new DevExpress.XtraPrinting.XlsExportOptions();
            //    GC_YKB.ExportToXls(saveFileDialog.FileName);
            //    DevExpress.XtraEditors.XtraMessageBox.Show("保存成功！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            //}

            string startDate = this.dtBegin.ToString("yyyy-MM-dd");//查询开始时间
            string endDate = this.dtBegin.ToString("yyyy-MM-dd");//查询结束时间
            GC_YKB.ExportDtToExcel($"渝快保对账{startDate}_{endDate}");
        }
    }
}
