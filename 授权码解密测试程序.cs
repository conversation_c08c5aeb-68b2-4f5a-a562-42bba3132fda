using System;
using System.Runtime.InteropServices;
using System.Text;

namespace 授权码解密测试
{
    /// <summary>
    /// 授权码解密测试程序
    /// 用于解密 ENCRYPT_CODE 并查看其包含的信息
    /// </summary>
    class Program
    {
        // 引用 EncryptDecrypt.dll 中的解密函数
        [DllImport(@"EncryptDecrypt.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode)]
        public static extern bool DecryptStringForCS(string cipher, StringBuilder outputText);

        /// <summary>
        /// 解密授权码
        /// </summary>
        /// <param name="encryptCode">要解密的授权码</param>
        /// <returns>解密后的内容</returns>
        public static string DecryptAuthCode(string encryptCode)
        {
            try
            {
                StringBuilder decryptedText = new StringBuilder(1000);
                string cleanCode = encryptCode.Replace("\r\n", "");
                
                if (DecryptStringForCS(cleanCode, decryptedText))
                {
                    return decryptedText.ToString();
                }
                else
                {
                    return "解密失败";
                }
            }
            catch (Exception ex)
            {
                return $"解密出错: {ex.Message}";
            }
        }

        /// <summary>
        /// 解析解密后的授权信息
        /// </summary>
        /// <param name="decryptedContent">解密后的内容</param>
        public static void ParseAuthInfo(string decryptedContent)
        {
            try
            {
                if (string.IsNullOrEmpty(decryptedContent) || decryptedContent == "解密失败")
                {
                    Console.WriteLine("无法解密授权码");
                    return;
                }

                // 解密后格式：医院名称,客户端数量,应用代码,过期日期,版本号
                string[] parts = decryptedContent.Split(',');
                
                if (parts.Length >= 5)
                {
                    Console.WriteLine("=== 授权码信息解析 ===");
                    Console.WriteLine($"医院名称: {parts[0]}");
                    Console.WriteLine($"客户端数量: {parts[1]}");
                    Console.WriteLine($"应用代码: {parts[2]}");
                    Console.WriteLine($"过期日期: {parts[3]}");
                    Console.WriteLine($"版本号: {parts[4]}");
                    
                    // 计算剩余天数
                    if (DateTime.TryParse(parts[3], out DateTime expireDate))
                    {
                        int remainingDays = (expireDate - DateTime.Now.Date).Days;
                        Console.WriteLine($"剩余天数: {remainingDays} 天");
                        
                        if (remainingDays > 30)
                        {
                            Console.WriteLine("状态: 授权有效");
                        }
                        else if (remainingDays > 0)
                        {
                            Console.WriteLine($"状态: 即将过期 (还有 {remainingDays} 天)");
                        }
                        else
                        {
                            Console.WriteLine("状态: 已过期");
                        }
                    }
                }
                else
                {
                    Console.WriteLine("授权码格式不正确");
                    Console.WriteLine($"解密内容: {decryptedContent}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"解析出错: {ex.Message}");
            }
        }

        static void Main(string[] args)
        {
            // 要解密的授权码
            string authCode = "5IpuNrEjV/co4Auv54nQt6tUnWAbEFfPSl5dAT4sksLv1gDWtm8KmzelW1Eh9hGViGv9Qi899Q212imFmzJz2ZChHG852Y5T+jMb9Q==hhZqw";
            
            Console.WriteLine("=== 天健HIS授权码解密工具 ===");
            Console.WriteLine($"授权码: {authCode}");
            Console.WriteLine();
            
            // 解密授权码
            string decryptedContent = DecryptAuthCode(authCode);
            
            // 解析并显示信息
            ParseAuthInfo(decryptedContent);
            
            Console.WriteLine();
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }
    }
}

/*
使用说明：
1. 将此文件保存为 AuthCodeDecrypt.cs
2. 确保 EncryptDecrypt.dll 在程序目录下
3. 编译并运行程序
4. 程序会显示授权码的详细信息，包括过期时间

编译命令：
csc AuthCodeDecrypt.cs

注意事项：
- 需要 EncryptDecrypt.dll 文件支持
- 如果解密失败，可能是 DLL 版本不匹配或路径问题
- 解密结果会显示医院名称、过期日期等敏感信息，请注意保密
*/
